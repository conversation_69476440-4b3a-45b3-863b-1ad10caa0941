"""Media download controller."""
import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from ..models.request_models import DownloadRequest
from ..models.response_models import DownloadResponse, ErrorResponse, HealthResponse
from ...application.dtos.download_dtos import DownloadMediaRequest
from ..dependencies import get_download_media_use_case

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["media"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.now().isoformat()
    )


@router.post("/download", response_model=DownloadResponse)
async def download_media(request: DownloadRequest):
    """Download media from Instagram or YouTube and send to Telegram."""
    try:
        logger.info(f"Received download request for URL: {request.url}")

        # Get use case with proper dependencies
        use_case = get_download_media_use_case(request.bot_token)

        # Convert to application DTO
        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token
        )

        # Execute use case
        result = await use_case.execute(app_request)

        # Convert to presentation response
        if result.success:
            return DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
        else:
            return JSONResponse(
                status_code=400,
                content=ErrorResponse(
                    status="error",
                    message=result.message
                ).model_dump()
            )

    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Invalid request data",
                detail=str(e)
            ).model_dump()
        )
    except Exception as e:
        logger.error(f"Unexpected error in media controller: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )
