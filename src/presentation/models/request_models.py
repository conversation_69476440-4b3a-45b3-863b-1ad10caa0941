"""Request models for the API."""
from pydantic import BaseModel, Field, validator


class DownloadRequest(BaseModel):
    """Request model for downloading media."""
    chat_id: int = Field(..., description="Telegram chat ID")
    url: str = Field(..., description="URL to download media from")
    bot_token: str = Field(..., description="Telegram bot token")

    @validator('chat_id')
    def validate_chat_id(cls, v):
        if not isinstance(v, int):
            raise ValueError('Chat ID must be an integer')
        return v

    @validator('url')
    def validate_url(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('URL cannot be empty')
        
        # Basic URL validation
        if not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('URL must start with http:// or https://')
        
        return v

    @validator('bot_token')
    def validate_bot_token(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Bot token cannot be empty')
        
        if len(v) < 10:
            raise ValueError('Bot token appears to be invalid')
        
        return v
