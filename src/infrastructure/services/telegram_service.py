"""Telegram service implementation."""
import os
import logging
from typing import <PERSON><PERSON>
from telegram import <PERSON><PERSON>
import telebot

from ...domain.interfaces.services import ITelegramService
from ...domain.value_objects.identifiers import ChatId, BotToken

logger = logging.getLogger(__name__)


class TelegramService(ITelegramService):
    """Telegram service implementation."""

    def __init__(self, bot_token: BotToken):
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token.value)
        self.telebot_instance = telebot.TeleBot(token=bot_token.value)
        
        # Configure telebot for large files
        self._configure_telebot()

    def _configure_telebot(self):
        """Configure telebot for large file uploads."""
        LOCAL_API_URL = "http://localhost:8081/bot{0}/{1}"
        CONNECTION_TIMEOUT = 300
        
        telebot.apihelper.API_URL = LOCAL_API_URL
        telebot.apihelper.READ_TIMEOUT = CONNECTION_TIMEOUT
        telebot.apihelper.CONNECT_TIMEOUT = CONNECTION_TIMEOUT

    async def send_photo(
        self, 
        chat_id: ChatId, 
        photo_path: str, 
        caption: Optional[str] = None
    ) -> str:
        """Send photo to Telegram chat. Returns file_id."""
        try:
            if not os.path.exists(photo_path):
                raise FileNotFoundError(f"Photo file not found: {photo_path}")

            with open(photo_path, "rb") as photo_file:
                msg = await self.bot.send_photo(
                    chat_id=chat_id.value,
                    photo=photo_file,
                    caption=caption
                )
                
            if msg and msg.photo:
                file_id = msg.photo[-1].file_id
                logger.info(f"Photo sent successfully, file_id: {file_id}")
                return file_id
            else:
                raise Exception("Failed to send photo - no file_id returned")
                
        except Exception as e:
            logger.error(f"Error sending photo: {e}")
            raise

    async def send_video(
        self, 
        chat_id: ChatId, 
        video_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None
    ) -> str:
        """Send video to Telegram chat. Returns file_id."""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            file_size = os.path.getsize(video_path) / (1024 * 1024)  # Size in MB

            if file_size > 2048:  # 2GB in MB
                raise ValueError("Video file size exceeds 2GB limit")

            # For large files, use telebot with local API
            if file_size > 50:  # Use telebot for files larger than 50MB
                return await self._send_large_video(
                    chat_id, video_path, caption, duration, width, height, thumbnail_path
                )
            else:
                # Use python-telegram-bot for smaller files
                with open(video_path, "rb") as video_file:
                    msg = await self.bot.send_video(
                        chat_id=chat_id.value,
                        video=video_file,
                        caption=caption,
                        duration=duration,
                        width=width,
                        height=height
                    )
                    
                if msg and msg.video:
                    file_id = msg.video.file_id
                    logger.info(f"Video sent successfully, file_id: {file_id}")
                    return file_id
                else:
                    raise Exception("Failed to send video - no file_id returned")
                    
        except Exception as e:
            logger.error(f"Error sending video: {e}")
            raise

    async def _send_large_video(
        self,
        chat_id: ChatId,
        video_path: str,
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None
    ) -> str:
        """Send large video using telebot with local API."""
        try:
            with open(video_path, 'rb') as video:
                thumbnail = None
                if thumbnail_path and os.path.exists(thumbnail_path):
                    thumbnail = open(thumbnail_path, 'rb')

                try:
                    msg = self.telebot_instance.send_video(
                        chat_id=chat_id.value,
                        video=video,
                        caption=caption,
                        duration=duration,
                        width=width,
                        height=height,
                        thumbnail=thumbnail,
                        supports_streaming=True
                    )
                    
                    if msg and msg.video:
                        file_id = msg.video.file_id
                        logger.info(f"Large video sent successfully, file_id: {file_id}")
                        return file_id
                    else:
                        raise Exception("Failed to send large video - no file_id returned")
                        
                finally:
                    if thumbnail:
                        thumbnail.close()
                        
        except Exception as e:
            logger.error(f"Error sending large video: {e}")
            raise

    async def send_message(self, chat_id: ChatId, text: str) -> None:
        """Send text message to Telegram chat."""
        try:
            await self.bot.send_message(chat_id=chat_id.value, text=text)
            logger.info(f"Message sent to chat {chat_id.value}")
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise
