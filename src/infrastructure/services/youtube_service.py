"""YouTube service implementation."""
import os
import re
import json
import logging
import subprocess
from typing import Dict, Union, Optional

from ...domain.interfaces.services import IYouTubeService
from ...domain.entities.download_result import DownloadResult
from ...domain.value_objects.url import Url

logger = logging.getLogger(__name__)


class YouTubeService(IYouTubeService):
    """YouTube service implementation."""

    def __init__(self):
        self.yt_dlp_available = self._check_yt_dlp()

    def _check_yt_dlp(self) -> bool:
        """Check if yt-dlp is available."""
        try:
            subprocess.run(["which", "yt-dlp"], capture_output=True, text=True, check=False)
            return True
        except Exception as e:
            logger.error(f"Error checking for yt-dlp: {e}")
            return False

    def _check_ffprobe(self) -> bool:
        """Check if ffprobe is available."""
        try:
            subprocess.run(["which", "ffprobe"], capture_output=True, text=True, check=False)
            return True
        except Exception as e:
            logger.error(f"Error checking for ffprobe: {e}")
            return False

    def _extract_video_metadata(self, video_path: str) -> Dict[str, Union[int, float, None]]:
        """Extract video metadata using ffprobe."""
        metadata = {
            "duration": None,
            "width": None,
            "height": None
        }

        if not self._check_ffprobe():
            logger.warning("ffprobe not available, cannot extract video metadata")
            return metadata

        try:
            command = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode == 0:
                data = json.loads(process.stdout)

                # Find video stream
                for stream in data.get("streams", []):
                    if stream.get("codec_type") == "video":
                        metadata["width"] = stream.get("width")
                        metadata["height"] = stream.get("height")

                        # Get duration from stream or format
                        duration = stream.get("duration")
                        if duration:
                            metadata["duration"] = int(float(duration))
                        break

                # If duration not found in stream, try format
                if not metadata["duration"]:
                    format_info = data.get("format", {})
                    duration = format_info.get("duration")
                    if duration:
                        metadata["duration"] = int(float(duration))

        except Exception as e:
            logger.error(f"Error extracting video metadata: {e}")

        return metadata

    def _generate_thumbnail(self, video_path: str, thumbnail_path: str) -> bool:
        """Generate a thumbnail from the video."""
        if not self._check_ffprobe():
            logger.warning("ffmpeg not available, cannot generate thumbnail")
            return False

        try:
            command = [
                "ffmpeg",
                "-i", video_path,
                "-ss", "00:00:01.000",  # Take screenshot at 1 second
                "-vframes", "1",
                "-y",  # Overwrite output file
                thumbnail_path
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode == 0 and os.path.exists(thumbnail_path):
                return True
            else:
                logger.error(f"Error generating thumbnail: {process.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error generating thumbnail: {e}")
            return False

    async def get_media_info(self, url: Url):
        """Get media information from URL."""
        # This method is required by the interface but not used for YouTube
        # YouTube downloads are handled directly
        raise NotImplementedError("Use download_video method for YouTube")

    async def download_media(self, media_item):
        """Download media content."""
        # This method is required by the interface but not used for YouTube
        # YouTube downloads are handled directly
        raise NotImplementedError("Use download_video method for YouTube")

    async def download_video(self, url: Url) -> DownloadResult:
        """Download a YouTube video."""
        if not self.yt_dlp_available:
            return DownloadResult(
                success=False,
                message="yt-dlp is not available"
            )

        try:
            # Create a temporary directory for the download
            timestamp = int(os.path.getmtime(__file__))
            download_dir = os.path.join("downloads")
            os.makedirs(download_dir, exist_ok=True)

            # Extract the video ID
            video_id = url.extract_youtube_video_id()
            if not video_id:
                return DownloadResult(
                    success=False,
                    message="Could not extract video ID from URL"
                )

            # Set the output template
            output_template = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.%(ext)s")

            # Download the video
            command = [
                "yt-dlp",
                "-f", "best[ext=mp4]/best",
                "-o", output_template,
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url.value
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode != 0:
                return DownloadResult(
                    success=False,
                    message=f"Error downloading video: {process.stderr}"
                )

            # Find the downloaded file
            expected_file = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.mp4")
            if not os.path.exists(expected_file):
                # Try to find any file with the video ID and timestamp
                for file in os.listdir(download_dir):
                    if f"youtube_{video_id}_{timestamp}" in file:
                        expected_file = os.path.join(download_dir, file)
                        break

            if not os.path.exists(expected_file):
                return DownloadResult(
                    success=False,
                    message="Could not find downloaded file"
                )

            # Get the video title
            title_command = [
                "yt-dlp",
                "--get-title",
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url.value
            ]

            title_process = subprocess.run(title_command, capture_output=True, text=True, check=False)
            title = title_process.stdout.strip()

            # Extract video metadata
            metadata = self._extract_video_metadata(expected_file)

            # Generate thumbnail
            thumbnail_path = expected_file.replace(".mp4", "_thumb.jpg")
            thumbnail_generated = self._generate_thumbnail(expected_file, thumbnail_path)

            # Return the result
            return DownloadResult(
                success=True,
                message="Video downloaded successfully",
                file_path=expected_file,
                title=title,
                duration=metadata["duration"],
                width=metadata["width"],
                height=metadata["height"],
                thumbnail_path=thumbnail_path if thumbnail_generated else None
            )

        except Exception as e:
            logger.error(f"Error downloading YouTube video: {e}")
            return DownloadResult(
                success=False,
                message=f"Error: {str(e)}"
            )

    async def download_audio(self, url: Url) -> DownloadResult:
        """Download a YouTube video as audio."""
        if not self.yt_dlp_available:
            return DownloadResult(
                success=False,
                message="yt-dlp is not available"
            )

        try:
            # Create a temporary directory for the download
            timestamp = int(os.path.getmtime(__file__))
            download_dir = os.path.join("downloads")
            os.makedirs(download_dir, exist_ok=True)

            # Extract the video ID
            video_id = url.extract_youtube_video_id()
            if not video_id:
                return DownloadResult(
                    success=False,
                    message="Could not extract video ID from URL"
                )

            # Set the output template
            output_template = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.%(ext)s")

            # Download the audio directly as mp3
            command = [
                "yt-dlp",
                "-f", "bestaudio[ext=m4a]/bestaudio",
                "-o", output_template.replace(".%(ext)s", ".mp3"),
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url.value
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode != 0:
                return DownloadResult(
                    success=False,
                    message=f"Error downloading audio: {process.stderr}"
                )

            # Find the downloaded file
            expected_file = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.mp3")
            if not os.path.exists(expected_file):
                # Try to find any file with the video ID and timestamp
                for file in os.listdir(download_dir):
                    if f"youtube_{video_id}_{timestamp}" in file:
                        expected_file = os.path.join(download_dir, file)
                        break

            if not os.path.exists(expected_file):
                return DownloadResult(
                    success=False,
                    message="Could not find downloaded file"
                )

            # Get the video title
            title_command = [
                "yt-dlp",
                "--get-title",
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url.value
            ]

            title_process = subprocess.run(title_command, capture_output=True, text=True, check=False)
            title = title_process.stdout.strip()

            return DownloadResult(
                success=True,
                message="Audio downloaded successfully",
                file_path=expected_file,
                title=title
            )

        except Exception as e:
            logger.error(f"Error downloading YouTube audio: {e}")
            return DownloadResult(
                success=False,
                message=f"Error: {str(e)}"
            )
