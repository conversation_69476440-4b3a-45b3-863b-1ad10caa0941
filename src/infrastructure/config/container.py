"""Dependency injection container."""
from typing import Dict, Any

from ...application.use_cases.download_media_use_case import DownloadMediaUseCase
from ...infrastructure.services.instagram_service import InstagramService
from ...infrastructure.services.youtube_service import YouTubeService
from ...infrastructure.services.telegram_service import TelegramService
from ...infrastructure.services.file_service import FileService
from ...infrastructure.repositories.sqlite_history_repository import SQLiteHistoryRepository
from ...infrastructure.repositories.file_media_repository import FileMediaRepository
from ...domain.value_objects.identifiers import BotToken


class Container:
    """Simple dependency injection container."""

    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._singletons: Dict[str, Any] = {}
        self._setup_services()

    def _setup_services(self):
        """Setup service registrations."""
        # Register singletons
        self._singletons['instagram_service'] = InstagramService()
        self._singletons['youtube_service'] = YouTubeService()
        self._singletons['file_service'] = FileService()
        self._singletons['history_repository'] = SQLiteHistoryRepository()
        self._singletons['media_repository'] = FileMediaRepository()

    def get_instagram_service(self) -> InstagramService:
        """Get Instagram service."""
        return self._singletons['instagram_service']

    def get_youtube_service(self) -> YouTubeService:
        """Get YouTube service."""
        return self._singletons['youtube_service']

    def get_file_service(self) -> FileService:
        """Get file service."""
        return self._singletons['file_service']

    def get_history_repository(self) -> SQLiteHistoryRepository:
        """Get history repository."""
        return self._singletons['history_repository']

    def get_media_repository(self) -> FileMediaRepository:
        """Get media repository."""
        return self._singletons['media_repository']

    def get_telegram_service(self, bot_token: str) -> TelegramService:
        """Get Telegram service (new instance for each bot token)."""
        return TelegramService(BotToken(bot_token))

    def get_download_media_use_case(self, bot_token: str) -> DownloadMediaUseCase:
        """Get download media use case."""
        return DownloadMediaUseCase(
            instagram_service=self.get_instagram_service(),
            youtube_service=self.get_youtube_service(),
            telegram_service=self.get_telegram_service(bot_token),
            file_service=self.get_file_service(),
            history_repository=self.get_history_repository(),
            media_repository=self.get_media_repository()
        )


# Global container instance
container = Container()
