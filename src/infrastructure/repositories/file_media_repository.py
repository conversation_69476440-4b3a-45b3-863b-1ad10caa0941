"""File system implementation of media repository."""
import os
import logging
from typing import Optional

from ...domain.interfaces.repositories import IMediaRepository

logger = logging.getLogger(__name__)


class FileMediaRepository(IMediaRepository):
    """File system implementation of media repository."""

    def __init__(self, base_directory: str = "downloads"):
        self.base_directory = base_directory
        self._ensure_directory_exists()

    def _ensure_directory_exists(self):
        """Ensure the base directory exists."""
        os.makedirs(self.base_directory, exist_ok=True)

    async def save_file(self, file_path: str, content: bytes) -> None:
        """Save file to storage."""
        try:
            # Ensure the directory exists
            directory = os.path.dirname(file_path)
            if directory:
                os.makedirs(directory, exist_ok=True)
            
            with open(file_path, "wb") as f:
                f.write(content)
                
            logger.info(f"Saved file: {file_path} ({len(content)} bytes)")
            
        except Exception as e:
            logger.error(f"Error saving file {file_path}: {e}")
            raise

    async def delete_file(self, file_path: str) -> None:
        """Delete file from storage."""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            raise

    async def file_exists(self, file_path: str) -> bool:
        """Check if file exists."""
        return os.path.exists(file_path)

    async def get_file_size(self, file_path: str) -> int:
        """Get file size in bytes."""
        try:
            if os.path.exists(file_path):
                return os.path.getsize(file_path)
            else:
                raise FileNotFoundError(f"File not found: {file_path}")
                
        except Exception as e:
            logger.error(f"Error getting file size for {file_path}: {e}")
            raise
