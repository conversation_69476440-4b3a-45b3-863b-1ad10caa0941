"""SQLite implementation of history repository."""
import sqlite3
import logging
from typing import List, Optional
from datetime import datetime

from ...domain.interfaces.repositories import IHistoryRepository
from ...domain.value_objects.identifiers import UserId
from ...domain.value_objects.url import Url

logger = logging.getLogger(__name__)


class SQLiteHistoryRepository(IHistoryRepository):
    """SQLite implementation of history repository."""

    def __init__(self, db_path: str = "sqlite3.db"):
        self.db_path = db_path
        self._init_db()

    def _init_db(self):
        """Initialize the database."""
        conn = sqlite3.connect(self.db_path)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS history 
                     (user_id INTEGER, url TEXT, file_id TEXT, timestamp TEXT)''')
        conn.commit()
        conn.close()

    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        return sqlite3.connect(self.db_path)

    async def save_download_history(
        self, 
        user_id: UserId, 
        url: Url, 
        telegram_file_id: str,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Save download history."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            timestamp_str = timestamp.isoformat() if timestamp else datetime.now().isoformat()
            
            c.execute(
                "INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, ?)",
                (user_id.value, url.value, telegram_file_id, timestamp_str)
            )
            conn.commit()
            conn.close()
            
            logger.info(f"Saved download history for user {user_id.value}")
            
        except Exception as e:
            logger.error(f"Error saving download history: {e}")
            raise

    async def get_user_history(self, user_id: UserId, limit: int = 10) -> List[dict]:
        """Get user download history."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            c.execute(
                "SELECT url, file_id, timestamp FROM history WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?",
                (user_id.value, limit)
            )
            
            rows = c.fetchall()
            conn.close()
            
            return [
                {
                    "url": row[0],
                    "file_id": row[1],
                    "timestamp": row[2]
                }
                for row in rows
            ]
            
        except Exception as e:
            logger.error(f"Error getting user history: {e}")
            raise

    async def url_exists_in_history(self, url: Url) -> bool:
        """Check if URL exists in history."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            c.execute("SELECT COUNT(*) FROM history WHERE url = ?", (url.value,))
            count = c.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception as e:
            logger.error(f"Error checking URL in history: {e}")
            raise
