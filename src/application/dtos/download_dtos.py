"""DTOs for download operations."""
from dataclasses import dataclass
from typing import Optional


@dataclass
class DownloadMediaRequest:
    """Request DTO for downloading media."""
    chat_id: int
    url: str
    bot_token: str


@dataclass
class DownloadMediaResponse:
    """Response DTO for downloading media."""
    success: bool
    message: str
    file_id: Optional[str] = None


@dataclass
class MediaInfoResponse:
    """Response DTO for media information."""
    media_type: str
    title: Optional[str] = None
    duration: Optional[int] = None
    width: Optional[int] = None
    height: Optional[int] = None
    caption: Optional[str] = None
    download_url: Optional[str] = None
